package com.taobao.wireless.orange.common.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ParameterValueType {
    JSON("JSON"),
    STRING("STRING"),
    BOOLEAN("BOOLEAN");

    private final String code;

    public static ParameterValueType getValueType(String code) {
        for (ParameterValueType type : ParameterValueType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
