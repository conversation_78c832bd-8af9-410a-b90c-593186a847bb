package com.taobao.wireless.orange.manager;

import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.ONamespaceVersionDAO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceDO;
import com.taobao.wireless.orange.dal.enhanced.entity.ONamespaceVersionDO;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class NamespaceVersionManager {

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;
    @Autowired
    private NamespaceManager namespaceManager;

    public List<ONamespaceVersionDO> getAvailableNamespaceVersions(String appKey) {
        return namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .list();
    }

    /**
     * 根据 changeVersionRange (左开右闭）区间查询有效的 namespaceVersion
     *
     * @param appKey
     * @param changeVersionRange
     * @return
     */
    public List<ONamespaceVersionDO> getAvailableNamespaceVersions(String appKey, Pair<String, String> changeVersionRange) {
        return namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getAppKey, appKey)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .gt(changeVersionRange.getLeft() != null, ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getLeft())
                .le(changeVersionRange.getRight() != null, ONamespaceVersionDO::getNamespaceChangeVersion, changeVersionRange.getRight())
                .list();
    }

    @Transactional
    public String upgradeVersion(String namespaceId, String releaseVersion, NamespaceVersionChangeType changeType) {
        // 失效当前版本
        invalidateCurrentVersion(namespaceId);

        // 创建新版本，两个版本号都使用新生成的版本
        String newVersion = String.valueOf(SerializeUtil.version());
        createNewNamespaceVersion(namespaceId, releaseVersion, changeType, newVersion, newVersion);

        return newVersion;
    }

    @Transactional
    public void upgradeChangeVersion(String namespaceId, String releaseVersion, NamespaceVersionChangeType changeType) {
        // 获取当前版本的 namespaceVersion，同时失效当前版本
        String currentNamespaceVersion = getCurrentNamespaceVersion(namespaceId);

        // 失效当前版本
        invalidateCurrentVersion(namespaceId);

        // 创建新版本，保持原有的 namespaceVersion，只更新 changeVersion
        String newChangeVersion = String.valueOf(SerializeUtil.version());
        createNewNamespaceVersion(namespaceId, releaseVersion, changeType, currentNamespaceVersion, newChangeVersion);
    }

    /**
     * 失效当前可用版本
     */
    private void invalidateCurrentVersion(String namespaceId) {
        namespaceVersionDAO.lambdaUpdate()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .set(ONamespaceVersionDO::getIsAvailable, Available.N)
                .update();
    }

    public String getCurrentNamespaceVersion(String namespaceId) {
        return namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getNamespaceId, namespaceId)
                .eq(ONamespaceVersionDO::getIsAvailable, Available.Y)
                .oneOpt()
                .map(ONamespaceVersionDO::getNamespaceVersion)
                .orElse(null);
    }

    /**
     * 创建新的命名空间版本
     */
    private void createNewNamespaceVersion(String namespaceId, String releaseVersion,
                                         NamespaceVersionChangeType changeType, String namespaceVersion, String changeVersion) {
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(namespaceId);

        ONamespaceVersionDO newNamespaceVersion = new ONamespaceVersionDO();
        newNamespaceVersion.setAppKey(namespace.getAppKey());
        newNamespaceVersion.setNamespaceId(namespaceId);
        newNamespaceVersion.setReleaseVersion(releaseVersion);
        newNamespaceVersion.setIsAvailable(Available.Y);
        newNamespaceVersion.setChangeType(changeType);
        newNamespaceVersion.setNamespaceVersion(namespaceVersion);
        newNamespaceVersion.setNamespaceChangeVersion(changeVersion);

        namespaceVersionDAO.save(newNamespaceVersion);
    }
}
