package com.taobao.wireless.orange.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taobao.unifiedsession.core.json.JSON;
import com.taobao.wireless.orange.common.constant.enums.*;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.SerializeUtil;
import com.taobao.wireless.orange.dal.enhanced.dao.*;
import com.taobao.wireless.orange.dal.enhanced.entity.*;
import com.taobao.wireless.orange.dal.enhanced.mapper.OConditionVersionMapper;
import com.taobao.wireless.orange.manager.config.model.Parameter;
import com.taobao.wireless.orange.manager.model.*;
import com.taobao.wireless.orange.manager.release.AbstractOperationTemplate;
import com.taobao.wireless.orange.manager.release.OperationContext;
import com.taobao.wireless.orange.manager.release.OperationFactory;
import com.taobao.wireless.orange.manager.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.taobao.wireless.orange.common.constant.Common.DEFAULT_CONDITION_ID;
import static com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType.FINISH_RELEASE;

@Slf4j
@Service
public class ReleaseOrderManager {
    @Autowired
    private ParameterManager parameterManager;
    @Autowired
    private NamespaceManager namespaceManager;

    @Autowired
    private ConditionManager conditionManager;

    @Autowired
    private OReleaseOrderDAO releaseOrderDAO;

    @Autowired
    private NamespaceVersionManager namespaceVersionManager;

    @Autowired
    private OParameterVersionDAO parameterVersionDAO;

    @Autowired
    private OConditionVersionDAO conditionVersionDAO;

    @Autowired
    private ONamespaceVersionContentDAO namespaceVersionContentDAO;

    @Autowired
    private OReleaseOrderOperationDAO releaseOrderOperationDAO;

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    private OParameterDAO parameterDAO;

    @Autowired
    private OperationFactory operationFactory;
    @Autowired
    private OConditionVersionMapper oConditionVersionMapper;
    @Autowired
    private OParameterConditionVersionDAO parameterConditionVersionDAO;

    /**
     * 查询发布单列表
     *
     * @param query
     * @param pagination
     * @return
     */
    public Page<OReleaseOrderDO> query(ReleaseOrderBO query, Pagination pagination) {
        return releaseOrderDAO.lambdaQuery()
                .eq(query.getAppKey() != null, OReleaseOrderDO::getAppKey, query.getAppKey())
                .eq(query.getNamespaceId() != null, OReleaseOrderDO::getNamespaceId, query.getNamespaceId())
                .eq(query.getReleaseVersion() != null, OReleaseOrderDO::getReleaseVersion, query.getReleaseVersion())
                .in(CollectionUtils.isNotEmpty(query.getStatuses()), OReleaseOrderDO::getStatus, query.getStatuses())
                .orderByDesc(OReleaseOrderDO::getId)
                .page(PageUtil.build(pagination));
    }

    /**
     * 新建发布
     *
     * @param releaseOrderBO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String create(ReleaseOrderBO releaseOrderBO) {
        ONamespaceDO namespace = namespaceManager.getByNamespaceId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setAppKey(namespace.getAppKey());
        // todo: 获取 namespace 锁

        // 检测涉及发布实体是否有在变更中
        checkReleaseObjectIsPublishing(releaseOrderBO);

        // 生成发布单
        String releaseVersion = createReleaseOrder(releaseOrderBO);

        // 新增条件版本记录
        conditionManager.createConditionVersions(namespace, releaseVersion, releaseOrderBO.getConditionVersions());

        // 新增参数版本记录
        parameterManager.createParameterVersions(namespace, releaseVersion, releaseOrderBO.getParameterVersions());

        // 新增参数条件版本记录
        parameterManager.createParameterConditionVersions(namespace, releaseVersion, releaseOrderBO.getParameterVersions(), releaseOrderBO.getConditionVersions());

        // 新增 namespace 版本记录
        namespaceVersionManager.upgradeChangeVersion(releaseOrderBO.getNamespaceId(), releaseVersion, NamespaceVersionChangeType.NEW_RELEASE);

        // todo: 释放 namespace 锁
        return releaseVersion;
    }

    public void publish(String releaseVersion) {
        AbstractOperationTemplate strategy =
                (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.RELEASE);

        OperationContext context = new OperationContext(releaseVersion);
        strategy.execute(context);
    }

    public void cancel(String releaseVersion) {
        AbstractOperationTemplate strategy =
                (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.CANCEL);

        OperationContext context = new OperationContext(releaseVersion);
        strategy.execute(context);
    }

    public void verifyReply(String releaseVersion, VerifyReplyBO verifyReply) {
        AbstractOperationTemplate strategy =
                (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.VERIFY_REPLY);

        OperationContext context = new OperationContext(releaseVersion);
        context.setAdditionalData(verifyReply);
        strategy.execute(context);
    }

    public void startVerify(String releaseVersion) {
        AbstractOperationTemplate strategy =
                (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.START_VERIFY);

        OperationContext context = new OperationContext(releaseVersion);
        strategy.execute(context);
    }

    public void ratioGray(String releaseVersion, int percent) {
        AbstractOperationTemplate strategy =
                (AbstractOperationTemplate) operationFactory.getStrategy(OperationType.RATIO_GRAY);

        OperationContext context = new OperationContext(releaseVersion, percent);
        strategy.execute(context);
    }

    /**
     * 查询发布单变更内容
     *
     * @param releaseVersion
     * @return
     */
    public ReleaseOrderChangeBO getChanges(String releaseVersion) {
        ReleaseOrderChangeBO releaseOrderChange = new ReleaseOrderChangeBO();
        String namespaceId = releaseOrderDAO.getByReleaseVersion(releaseVersion).getNamespaceId();
        releaseOrderChange.setNamespaceId(namespaceId);

        Map<String, List<OParameterConditionVersionDO>> parameterConditionsMap = parameterConditionVersionDAO.lambdaQuery()
                .eq(OParameterConditionVersionDO::getReleaseVersion, releaseVersion).list().stream()
                .collect(Collectors.groupingBy(OParameterConditionVersionDO::getParameterId));
        List<OParameterVersionDO> parameterVersions = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion).list();
        List<ParameterVersionBO> parameterChanges = BeanUtil.createFromProperties(parameterVersions, ParameterVersionBO.class);
        parameterChanges.forEach(p -> {
            p.setParameterConditionVersions(BeanUtil.createFromProperties(parameterConditionsMap.get(p.getParameterId()), ParameterConditionVersionBO.class));
        });
        releaseOrderChange.setParameterChanges(parameterChanges);

        List<OConditionVersionDO> conditionVersions = conditionVersionDAO.lambdaQuery()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();
        releaseOrderChange.setConditionChanges(BeanUtil.createFromProperties(conditionVersions, ConditionVersionBO.class));

        fillPreviousParametersAndConditions(releaseOrderChange, namespaceId, releaseVersion);

        return releaseOrderChange;
    }

    /**
     * 获取改动前的参数版本
     *
     * @param releaseVersion
     * @return
     */
    private void fillPreviousParametersAndConditions(ReleaseOrderChangeBO releaseOrderChange, String namespaceId, String releaseVersion) {
        String namespaceVersion = namespaceVersionDAO.lambdaQuery()
                .eq(ONamespaceVersionDO::getReleaseVersion, releaseVersion)
                .eq(ONamespaceVersionDO::getChangeType, FINISH_RELEASE)
                .oneOpt()
                .map(ONamespaceVersionDO::getNamespaceVersion)
                .orElse(null);


        var namespaceVersionContent = namespaceVersionContentDAO.lambdaQuery()
                .eq(ONamespaceVersionContentDO::getNamespaceId, namespaceId)
                .le(namespaceVersion != null, ONamespaceVersionContentDO::getNamespaceVersion, namespaceVersion)
                .orderByDesc(ONamespaceVersionContentDO::getNamespaceVersion)
                .last("limit 1")
                .oneOpt()
                .orElseThrow(() -> new CommonException(ExceptionEnum.NAMESPACE_VERSION_CONTENT_NOT_EXIST));
        NamespaceVersionContentBO namespaceVersionContentBO = JSON.parse(namespaceVersionContent.getContent(), NamespaceVersionContentBO.class);
        releaseOrderChange.setPreviousNamespaceVersion(namespaceVersionContent.getNamespaceVersion());

        releaseOrderChange.setPreviousParameters(convertParametersTOBOS(namespaceVersionContentBO.getParameters()));
        releaseOrderChange.setPreviousConditions(convertConditionsToBOS(namespaceVersionContentBO.getConditions()));
    }

    private List<ParameterBO> convertParametersTOBOS(List<Parameter> parameters) {
        return parameters.stream().map(parameter -> {
            ParameterVersionBO parameterVersionBO = new ParameterVersionBO();
            ParameterBO parameterBO = new ParameterBO();
            parameterBO.setParameterKey(parameter.getKey());

            var valueType = ParameterValueType.getValueType(parameter.getValueType().toUpperCase());
            parameterBO.setValueType(valueType);
            parameterVersionBO.setValueType(valueType);
            parameterVersionBO.setReleaseVersion(String.valueOf(parameter.getVersion()));
            parameterBO.setParameterVersion(parameterVersionBO);

            List<OParameterConditionVersionDO> parameterConditionVersions = parameter.getConditionalValues().stream().map(condition -> {
                OParameterConditionVersionDO parameterConditionVersionDO = new OParameterConditionVersionDO();
                parameterConditionVersionDO.setConditionId(condition.getConditionId());
                parameterConditionVersionDO.setValue(condition.getValue().toString());
                return parameterConditionVersionDO;
            }).collect(Collectors.toList());
            OParameterConditionVersionDO defaultCondition = new OParameterConditionVersionDO();
            defaultCondition.setConditionId(DEFAULT_CONDITION_ID);
            defaultCondition.setValue(parameter.getDefaultValue().toString());
            parameterConditionVersions.add(defaultCondition);
            parameterBO.setParameterConditionVersions(parameterConditionVersions);
            return parameterBO;
        }).collect(Collectors.toList());
    }

    private List<ConditionBO> convertConditionsToBOS(List<NamespaceVersionContentBO.Condition> conditions) {
        return conditions.stream().map(condition -> {
            ConditionBO conditionBO = new ConditionBO();
            conditionBO.setConditionId(condition.getConditionId());
            conditionBO.setName(condition.getName());
            ConditionVersionBO conditionVersionBO = new ConditionVersionBO();
            conditionBO.setConditionVersion(conditionVersionBO);

            conditionVersionBO.setConditionId(condition.getConditionId());
            conditionVersionBO.setExpression(JSON.toJSONString(condition.getExpression()));
            conditionVersionBO.setReleaseVersion(condition.getReleaseVersion());
            return conditionBO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取发布单操作记录
     *
     * @param releaseVersion
     * @return
     */
    public List<OReleaseOrderOperationDO> getOperations(String releaseVersion, List<OperationType> operationTypes) {
        return releaseOrderOperationDAO.lambdaQuery()
                .eq(OReleaseOrderOperationDO::getReleaseVersion, releaseVersion)
                .in(CollectionUtils.isNotEmpty(operationTypes), OReleaseOrderOperationDO::getType, operationTypes)
                .orderByDesc(OReleaseOrderOperationDO::getId)
                .list();
    }

    /**
     * 获取发布单详情
     *
     * @param releaseVersion
     * @return
     */
    public ReleaseOrderBO getDetail(String releaseVersion) {
        OReleaseOrderDO releaseOrderDO = releaseOrderDAO.getByReleaseVersion(releaseVersion);
        ReleaseOrderBO releaseOrder = BeanUtil.createFromProperties(releaseOrderDO, ReleaseOrderBO.class);
        List<OParameterVersionDO> parameters = parameterVersionDAO.lambdaQuery()
                .eq(OParameterVersionDO::getReleaseVersion, releaseVersion)
                .list();
        List<OConditionVersionDO> conditions = conditionVersionDAO.lambdaQuery()
                .eq(OConditionVersionDO::getReleaseVersion, releaseVersion)
                .list();

        releaseOrder.setParameterVersions(BeanUtil.createFromProperties(parameters, ParameterVersionBO.class));
        releaseOrder.setConditionVersions(BeanUtil.createFromProperties(conditions, ConditionVersionBO.class));
        return releaseOrder;
    }


    private String createReleaseOrder(ReleaseOrderBO releaseOrderBO) {
        String releaseVersion = String.valueOf(SerializeUtil.version());
        releaseOrderBO.setReleaseVersion(releaseVersion);
        releaseOrderBO.setBizType(ReleaseOrderBizType.NAMESPACE);
        releaseOrderBO.setBizId(releaseOrderBO.getNamespaceId());
        releaseOrderBO.setStatus(ReleaseOrderStatus.INIT);
        releaseOrderBO.setReleaseType(ReleaseType.PUBLISH);

        // 新增发布单
        boolean success = releaseOrderDAO.save(releaseOrderBO);
        if (!success) {
            throw new CommonException(ExceptionEnum.CREATE_RELEASE_ORDER_FAIL);
        }
        return releaseVersion;
    }

    /**
     * 检测发布实体是否在发布中
     *
     * @param releaseOrder
     */
    private void checkReleaseObjectIsPublishing(ReleaseOrderBO releaseOrder) {
        if (CollectionUtils.isNotEmpty(releaseOrder.getParameterVersions())) {
            List<String> parameterIds = releaseOrder.getParameterVersions().stream()
                    .map(ParameterVersionBO::getParameterId)
                    .collect(Collectors.toList());

            Long count = parameterVersionDAO.lambdaQuery()
                    .in(OParameterVersionDO::getParameterId, parameterIds)
                    .eq(OParameterVersionDO::getStatus, VersionStatus.INIT)
                    .count();
            if (count > 0) {
                throw new CommonException(ExceptionEnum.PARAMETER_IS_PUBLISHING);
            }
        }

        if (CollectionUtils.isNotEmpty(releaseOrder.getConditionVersions())) {
            List<String> conditionIds = releaseOrder.getConditionVersions().stream()
                    .map(ConditionVersionBO::getConditionId)
                    .collect(Collectors.toList());

            Long count = conditionVersionDAO.lambdaQuery()
                    .in(OConditionVersionDO::getConditionId, conditionIds)
                    .eq(OConditionVersionDO::getStatus, VersionStatus.INIT)
                    .count();
            if (count > 0) {
                throw new CommonException(ExceptionEnum.CONDITION_IS_PUBLISHING);
            }
        }
    }
}